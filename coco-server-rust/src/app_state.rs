use crate::auth::token_blacklist::TokenBlacklist;
use crate::config::config_manager::ConfigManager;
use crate::services::token_service::TokenService;
use std::sync::Arc;

/// 应用状态结构
#[derive(Clone)]
pub struct AppState {
    pub config_manager: Arc<ConfigManager>,
    pub token_service: Arc<TokenService>,
    pub token_blacklist: Arc<TokenBlacklist>,
}

impl AppState {
    pub fn new(
        config_manager: Arc<ConfigManager>,
        token_service: Arc<TokenService>,
        token_blacklist: Arc<TokenBlacklist>,
    ) -> Self {
        Self {
            config_manager,
            token_service,
            token_blacklist,
        }
    }
}
