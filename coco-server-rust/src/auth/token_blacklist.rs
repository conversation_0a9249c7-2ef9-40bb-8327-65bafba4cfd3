use std::collections::HashSet;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// 令牌黑名单管理器
/// 用于管理已注销的JWT令牌，防止被重复使用
#[derive(Debug, Clone)]
pub struct TokenBlacklist {
    /// 黑名单令牌集合
    blacklisted_tokens: Arc<RwLock<HashSet<String>>>,
}

impl TokenBlacklist {
    /// 创建新的令牌黑名单实例
    pub fn new() -> Self {
        Self {
            blacklisted_tokens: Arc::new(RwLock::new(HashSet::new())),
        }
    }

    /// 将令牌添加到黑名单
    pub async fn add_token(&self, token: &str) {
        let mut blacklist = self.blacklisted_tokens.write().await;
        let token_hash = self.hash_token(token);
        
        if blacklist.insert(token_hash.clone()) {
            info!("Token added to blacklist: {}", self.mask_token(token));
            debug!("Blacklist size: {}", blacklist.len());
        } else {
            warn!("Token already in blacklist: {}", self.mask_token(token));
        }
    }

    /// 检查令牌是否在黑名单中
    pub async fn is_blacklisted(&self, token: &str) -> bool {
        let blacklist = self.blacklisted_tokens.read().await;
        let token_hash = self.hash_token(token);
        let is_blacklisted = blacklist.contains(&token_hash);
        
        if is_blacklisted {
            debug!("Token found in blacklist: {}", self.mask_token(token));
        }
        
        is_blacklisted
    }

    /// 获取黑名单中的令牌数量
    pub async fn size(&self) -> usize {
        let blacklist = self.blacklisted_tokens.read().await;
        blacklist.len()
    }

    /// 清空黑名单（谨慎使用）
    pub async fn clear(&self) {
        let mut blacklist = self.blacklisted_tokens.write().await;
        let count = blacklist.len();
        blacklist.clear();
        info!("Cleared {} tokens from blacklist", count);
    }

    /// 移除特定令牌（用于测试或管理）
    pub async fn remove_token(&self, token: &str) -> bool {
        let mut blacklist = self.blacklisted_tokens.write().await;
        let token_hash = self.hash_token(token);
        let removed = blacklist.remove(&token_hash);
        
        if removed {
            info!("Token removed from blacklist: {}", self.mask_token(token));
        }
        
        removed
    }

    /// 获取黑名单统计信息
    pub async fn get_stats(&self) -> BlacklistStats {
        let blacklist = self.blacklisted_tokens.read().await;
        BlacklistStats {
            total_tokens: blacklist.len(),
        }
    }

    /// 对令牌进行哈希处理以保护隐私
    /// 使用SHA256哈希，只存储哈希值而不是原始令牌
    fn hash_token(&self, token: &str) -> String {
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(token.as_bytes());
        format!("{:x}", hasher.finalize())
    }

    /// 掩码令牌用于日志记录
    fn mask_token(&self, token: &str) -> String {
        if token.len() <= 8 {
            "*".repeat(token.len())
        } else {
            format!("{}...{}", &token[..4], &token[token.len()-4..])
        }
    }
}

impl Default for TokenBlacklist {
    fn default() -> Self {
        Self::new()
    }
}

/// 黑名单统计信息
#[derive(Debug, Clone)]
pub struct BlacklistStats {
    /// 黑名单中的令牌总数
    pub total_tokens: usize,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_add_and_check_token() {
        let blacklist = TokenBlacklist::new();
        let token = "test-jwt-token-12345";

        // 初始状态应该不在黑名单中
        assert!(!blacklist.is_blacklisted(token).await);

        // 添加到黑名单
        blacklist.add_token(token).await;

        // 现在应该在黑名单中
        assert!(blacklist.is_blacklisted(token).await);

        // 检查大小
        assert_eq!(blacklist.size().await, 1);
    }

    #[tokio::test]
    async fn test_duplicate_add() {
        let blacklist = TokenBlacklist::new();
        let token = "test-jwt-token-12345";

        // 添加两次相同的令牌
        blacklist.add_token(token).await;
        blacklist.add_token(token).await;

        // 大小应该仍然是1
        assert_eq!(blacklist.size().await, 1);
        assert!(blacklist.is_blacklisted(token).await);
    }

    #[tokio::test]
    async fn test_remove_token() {
        let blacklist = TokenBlacklist::new();
        let token = "test-jwt-token-12345";

        // 添加令牌
        blacklist.add_token(token).await;
        assert!(blacklist.is_blacklisted(token).await);

        // 移除令牌
        let removed = blacklist.remove_token(token).await;
        assert!(removed);
        assert!(!blacklist.is_blacklisted(token).await);
        assert_eq!(blacklist.size().await, 0);
    }

    #[tokio::test]
    async fn test_clear_blacklist() {
        let blacklist = TokenBlacklist::new();
        
        // 添加多个令牌
        blacklist.add_token("token1").await;
        blacklist.add_token("token2").await;
        blacklist.add_token("token3").await;
        
        assert_eq!(blacklist.size().await, 3);

        // 清空黑名单
        blacklist.clear().await;
        assert_eq!(blacklist.size().await, 0);
        
        // 所有令牌都应该不在黑名单中
        assert!(!blacklist.is_blacklisted("token1").await);
        assert!(!blacklist.is_blacklisted("token2").await);
        assert!(!blacklist.is_blacklisted("token3").await);
    }

    #[tokio::test]
    async fn test_get_stats() {
        let blacklist = TokenBlacklist::new();
        
        // 初始统计
        let stats = blacklist.get_stats().await;
        assert_eq!(stats.total_tokens, 0);

        // 添加令牌后的统计
        blacklist.add_token("token1").await;
        blacklist.add_token("token2").await;
        
        let stats = blacklist.get_stats().await;
        assert_eq!(stats.total_tokens, 2);
    }

    #[tokio::test]
    async fn test_token_hashing() {
        let blacklist = TokenBlacklist::new();
        let token1 = "same-token";
        let token2 = "same-token";
        let token3 = "different-token";

        // 相同的令牌应该产生相同的哈希
        let hash1 = blacklist.hash_token(token1);
        let hash2 = blacklist.hash_token(token2);
        let hash3 = blacklist.hash_token(token3);

        assert_eq!(hash1, hash2);
        assert_ne!(hash1, hash3);
    }
}
