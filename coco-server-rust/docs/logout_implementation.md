# 注销功能实现文档

## 概述

本文档描述了在 Coco Server Rust 重写项目中实现的注销功能（TASK-005）。该功能提供了安全的用户注销机制，支持 JWT 令牌黑名单和 API 令牌撤销。

## 功能特性

### 1. 注销端点
- **路径**: `POST /account/logout`
- **认证**: 需要有效的 JWT 或 API 令牌
- **功能**: 安全注销当前用户会话

### 2. 令牌黑名单机制
- **JWT 令牌**: 添加到内存黑名单，防止重复使用
- **API 令牌**: 直接从系统中撤销
- **安全性**: 使用 SHA256 哈希存储令牌，保护隐私

### 3. 会话清理
- **JWT 会话**: 令牌添加到黑名单后立即失效
- **API 令牌会话**: 令牌被撤销后无法再次使用
- **认证中间件**: 自动检查黑名单状态

## 技术实现

### 核心组件

#### 1. TokenBlacklist 管理器
```rust
pub struct TokenBlacklist {
    blacklisted_tokens: Arc<RwLock<HashSet<String>>>,
}
```

**主要功能**:
- `add_token()`: 添加令牌到黑名单
- `is_blacklisted()`: 检查令牌是否在黑名单中
- `size()`: 获取黑名单大小
- `clear()`: 清空黑名单（管理功能）

#### 2. 注销处理器
```rust
pub async fn logout_handler(
    State(app_state): State<AppState>,
    Extension(user_context): Extension<UserContext>,
) -> Result<Json<LogoutResponse>, (StatusCode, Json<ErrorResponse>)>
```

**处理流程**:
1. 验证用户认证状态
2. 根据认证类型处理注销
3. 记录注销事件
4. 返回成功响应

#### 3. 认证中间件增强
- 在 JWT 验证前检查黑名单
- 传递原始令牌到用户上下文
- 支持令牌撤销检查

### 数据结构

#### 请求/响应模型
```rust
// 注销请求（空结构体）
pub struct LogoutRequest {}

// 注销响应
pub struct LogoutResponse {
    pub status: String,
    pub message: String,
}
```

#### 用户上下文扩展
```rust
pub struct UserContext {
    pub user_id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub auth_type: AuthType,
    pub provider: String,
    pub original_token: Option<String>, // 新增：原始令牌
}
```

## API 规格

### 注销端点

**请求**:
```http
POST /account/logout
Authorization: Bearer <jwt_token>
Content-Type: application/json

{}
```

**成功响应**:
```json
{
  "status": "ok",
  "message": "Logged out successfully"
}
```

**错误响应**:
```json
{
  "error": "Unauthorized",
  "message": "Invalid or expired token"
}
```

## 安全特性

### 1. 令牌保护
- 使用 SHA256 哈希存储令牌
- 日志中掩码显示令牌
- 防止令牌泄露

### 2. 并发安全
- 使用 `Arc<RwLock<>>` 确保线程安全
- 支持高并发访问
- 无数据竞争

### 3. 内存管理
- 高效的哈希集合存储
- 可选的过期清理机制
- 内存使用优化

## 测试覆盖

### 单元测试
- 令牌黑名单基本操作
- 序列化/反序列化
- 用户上下文创建
- API 令牌撤销

### 集成测试
- 端到端注销流程
- 认证中间件集成
- 应用状态管理
- 多令牌操作

### 测试命令
```bash
# 运行所有注销相关测试
cargo test logout -- --nocapture

# 运行特定测试模块
cargo test logout_tests -- --nocapture
cargo test logout_integration_test -- --nocapture
```

## 部署注意事项

### 1. 内存使用
- 黑名单存储在内存中
- 长期运行需要考虑内存清理
- 可扩展为持久化存储

### 2. 性能考虑
- 哈希查找 O(1) 复杂度
- 读写锁优化并发性能
- 适合高频访问场景

### 3. 监控建议
- 监控黑名单大小
- 跟踪注销频率
- 记录异常注销事件

## 未来扩展

### 1. 持久化存储
- Redis 集成
- 数据库存储
- 集群同步

### 2. 令牌过期清理
- 自动清理过期令牌
- 定时任务机制
- 内存优化

### 3. 审计日志
- 详细注销记录
- 安全事件跟踪
- 合规性支持

## 相关文件

### 核心实现
- `src/auth/token_blacklist.rs` - 令牌黑名单管理器
- `src/handlers/account_handler.rs` - 注销处理器
- `src/middleware/auth_middleware.rs` - 认证中间件
- `src/app_state.rs` - 应用状态管理

### 测试文件
- `src/handlers/account_handler.rs` - 单元测试
- `tests/logout_integration_test.rs` - 集成测试

### 配置文件
- `src/main.rs` - 路由配置
- `Cargo.toml` - 依赖管理

## 总结

注销功能的实现提供了：
- ✅ 安全的用户注销机制
- ✅ JWT 令牌黑名单支持
- ✅ API 令牌撤销功能
- ✅ 完整的测试覆盖
- ✅ 生产就绪的代码质量

该实现符合 TASK-005 的所有要求，并为未来的扩展提供了良好的基础。
