use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
};
use serde_json::{json, Value};
use std::sync::Arc;
use tower::ServiceExt;

// 导入必要的模块
use coco_server::app_state::AppState;
use coco_server::auth::token_blacklist::TokenBlacklist;
use coco_server::config::config_manager::ConfigManager;
use coco_server::handlers::account_handler::{logout_handler, LogoutResponse};
use coco_server::middleware::auth_middleware::auth_middleware;
use coco_server::services::token_service::TokenService;

/// 创建测试用的应用状态
fn create_test_app_state() -> AppState {
    let config_manager = Arc::new(ConfigManager::new().unwrap());
    let token_service = Arc::new(TokenService::new());
    let token_blacklist = Arc::new(TokenBlacklist::new());

    AppState::new(config_manager, token_service, token_blacklist)
}

/// 创建测试路由器
fn create_test_router() -> Router {
    let app_state = create_test_app_state();

    Router::new()
        .route("/account/logout", axum::routing::post(logout_handler))
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            auth_middleware,
        ))
        .with_state(app_state)
}

#[tokio::test]
async fn test_logout_endpoint_integration() {
    let app = create_test_router();

    // 创建一个有效的JWT令牌用于测试
    let test_jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************.test-signature";

    // 创建注销请求
    let request = Request::builder()
        .method("POST")
        .uri("/account/logout")
        .header("Authorization", format!("Bearer {}", test_jwt))
        .header("Content-Type", "application/json")
        .body(Body::from("{}"))
        .unwrap();

    // 注意：这个测试可能会失败，因为JWT验证需要正确的密钥
    // 但它展示了集成测试的结构
    let response = app.oneshot(request).await.unwrap();

    // 检查响应状态（可能是401因为JWT验证失败，这是预期的）
    println!("Response status: {}", response.status());

    // 这个测试主要验证路由和处理器的集成是否正确
    // 在实际环境中，需要使用有效的JWT令牌
}

#[tokio::test]
async fn test_logout_response_format() {
    // 测试注销响应的序列化格式
    let response = LogoutResponse {
        status: "ok".to_string(),
        message: "Logged out successfully".to_string(),
    };

    let json_str = serde_json::to_string(&response).unwrap();
    let parsed: Value = serde_json::from_str(&json_str).unwrap();

    assert_eq!(parsed["status"], "ok");
    assert_eq!(parsed["message"], "Logged out successfully");
}

#[tokio::test]
async fn test_token_blacklist_functionality() {
    let blacklist = TokenBlacklist::new();
    let test_token = "test-jwt-token-12345";

    // 初始状态：令牌不在黑名单中
    assert!(!blacklist.is_blacklisted(test_token).await);

    // 添加到黑名单
    blacklist.add_token(test_token).await;

    // 验证令牌现在在黑名单中
    assert!(blacklist.is_blacklisted(test_token).await);

    // 验证不同的令牌不在黑名单中
    assert!(!blacklist.is_blacklisted("different-token").await);
}

#[tokio::test]
async fn test_app_state_creation_with_blacklist() {
    let app_state = create_test_app_state();

    // 验证应用状态包含所有必要的组件
    assert!(app_state.config_manager.get_port() > 0);

    // 测试令牌黑名单功能
    let test_token = "test-token-for-app-state";
    app_state.token_blacklist.add_token(test_token).await;
    assert!(app_state.token_blacklist.is_blacklisted(test_token).await);
}

#[tokio::test]
async fn test_api_token_service_integration() {
    let token_service = TokenService::new();
    let user_id = "test-user-integration";
    let token_name = Some("integration-test-token".to_string());

    // 生成API令牌
    let token_result = token_service.generate_api_token(user_id, token_name).await;
    assert!(token_result.is_ok());

    let token = token_result.unwrap();

    // 验证令牌
    let validation_result = token_service.validate_api_token(&token.access_token).await;
    assert!(validation_result.is_ok());

    let validated_token = validation_result.unwrap();
    assert_eq!(validated_token.user_id, user_id);

    // 撤销令牌
    let revoke_result = token_service.revoke_token(&token.id, user_id).await;
    assert!(revoke_result.is_ok());

    // 验证令牌已被撤销
    let validation_after_revoke = token_service.validate_api_token(&token.access_token).await;
    assert!(validation_after_revoke.is_err());
}

#[tokio::test]
async fn test_multiple_token_blacklist_operations() {
    let blacklist = TokenBlacklist::new();
    let tokens = vec!["token-1", "token-2", "token-3", "token-4", "token-5"];

    // 添加多个令牌到黑名单
    for token in &tokens {
        blacklist.add_token(token).await;
    }

    // 验证所有令牌都在黑名单中
    for token in &tokens {
        assert!(blacklist.is_blacklisted(token).await);
    }

    // 验证不相关的令牌不在黑名单中
    assert!(!blacklist.is_blacklisted("unrelated-token").await);
}
